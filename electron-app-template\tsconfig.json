{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "node",
    "jsx": "preserve",
    "strict": false,
    "resolveJsonModule": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "noImplicitAny": false,
    // 使用装饰器
    "experimentalDecorators": true,
    // 使用元数据
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "lib": [
      "esnext",
      "dom"
    ],
    "types": [
      "vite/client",
      "./src/preload",
      "unplugin-vue-define-options/macros-global"
    ],
    "paths": {
      "@render/*": [
        "src/render/*"
      ],
      "@main/*": [
        "src/main/*"
      ],
      "@common/*": [
        "src/common/*"
      ]
    },
    "allowJs": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}
